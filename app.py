from datetime import datetime, timedelta
import sqlite3, time, functools, json, jwt, asyncio, pytz
from logdata import db_path, init_db
import pandas as pd
from Alerts.Process_Wrapper import run_fetch_task, run_update_task, run_fix_task
from DatabaseManagement.ImportExport import get_table_from_GZ
from flask import Flask, render_template, request, redirect, url_for, session, Response, jsonify
from waitress import serve

# Import the visualizer API, plaintiff review API, plaintiff management, and TRO IP
from app_visualizer import init_visualizer_routes
# Corrected import name for plaintiff review routes
from app_plaintiff_review import init_plaintiff_review_routes
from app_plaintiffs import init_plaintiffs_routes
from app_tro_ip import init_tro_ip_routes

# Import cache manager to ensure it's loaded on startup and accessible
# And import the refresh function directly
from cache_manager import refresh_cached_data, cached_cases_df, cached_plaintiff_df

app = Flask(__name__)
app.secret_key = 'AeRthskhg683496^59475009fjekt'  # Replace with a secure secret key
app.config['JWT_SECRET_KEY'] = 'AeRthskhg683496^59475009fjekt879846'  # Change this to a secure key
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# Import authentication decorator
from auth_decorators import login_required

# Initialize the visualizer API, plaintiff review API, plaintiff management, and TRO IP
app = init_visualizer_routes(app)
# Corrected function name from the import
app = init_plaintiff_review_routes(app)
app = init_plaintiffs_routes(app)
app = init_tro_ip_routes(app)

def token_required(f):
    @functools.wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Check if token is in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                app.logger.info(f"Invalid token format in request: {request}") # Log the request
                return jsonify({'message': 'Invalid token format'}), 401

        if not token:
            app.logger.info(f"Token is missing in request: {request}") # Log the request
            return jsonify({'message': 'Token is missing'}), 401

        try:
            # Verify token
            data = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
            current_user = data['user']
        except jwt.ExpiredSignatureError:
            app.logger.info(f"Token has expired in request: {request}") # Log the request
            return jsonify({'message': 'Token has expired'}), 401
        except jwt.InvalidTokenError:
            app.logger.info(f"Invalid token in request: {request}") # Log the request
            return jsonify({'message': 'Invalid token'}), 401

        return f(*args, **kwargs)
    return decorated

# Login route
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        if username == 'serge' and password == 'maidalv888':  # Hardcoded credentials
            session['logged_in'] = True
            return redirect(url_for('admin'))
        else:
            return render_template('login.html', error='Invalid credentials')
    return render_template('login.html')

# create token for product 3
@app.route('/authenticate', methods=['POST'])
def authenticate():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    if username == 'java_springboot' and password == '%6wjfhtf84687DFSJTfde484':  # Replace with proper authentication
        token = jwt.encode({
            'user': username,
            'exp': datetime.utcnow() + app.config['JWT_ACCESS_TOKEN_EXPIRES']
        }, app.config['JWT_SECRET_KEY'], algorithm='HS256')

        return jsonify({
            'status': 'success',
            'token': token
        })
    else:
        return jsonify({
            'status': 'error',
            'message': 'Invalid credentials'
        }), 401

# Logout route
@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    return redirect(url_for('login'))

# Main page
@app.route('/admin')
@login_required
def admin():
    return render_template('admin.html')


@app.route('/start_task', methods=['POST'])
@login_required
def start_task():
    try:
        selected_date = request.json.get('selectedDate')
        loop_back_days = request.json.get('loopBackDays')
        force_redo = request.json.get('forceRedo', False)

        result = run_fetch_task(selected_date, loop_back_days, force_redo)

        if result.get('error'):
            return jsonify(result), 500
        elif result.get('status') == 'failed_to_start':
            return jsonify(result), 500
        else:
            return jsonify(result)

    except Exception as e:
        print(f"Error in start_task: {str(e)}")
        return jsonify({'error': str(e)}), 500



@app.route('/start_update_task', methods=['POST'])
@login_required
def start_update_task():
    try:
        threshold_days = request.json.get('loopBackDays')

        result = run_update_task(threshold_days)

        if result.get('error'):
            return jsonify(result), 500
        elif result.get('status') == 'failed_to_start':
            return jsonify(result), 500
        else:
            return jsonify(result)

    except Exception as e:
        print(f"Error in start_update_task: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/stream/<int:run_id>')
@login_required
def stream(run_id):
    def event_stream():
        last_log_id = 0
        last_log_timestamp = None
        last_status = None
        while True:
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()

                # Modified query to include timestamp
                c.execute('''
                    SELECT
                        'log' as type,
                        logs.id as log_id,
                        steps.name as step_name,
                        logs.message as log_message,
                        logs.timestamp as log_timestamp,
                        NULL as run_status,
                        NULL as step_status
                    FROM logs
                    JOIN steps ON logs.step_id = steps.id
                    WHERE logs.run_id = ? AND (logs.id > ? OR logs.timestamp > ?)
                    UNION ALL
                    SELECT
                        'status' as type,
                        NULL as log_id,
                        steps.name as step_name,
                        NULL as log_message,
                        NULL as log_timestamp,
                        runs.status as run_status,
                        steps.status as step_status
                    FROM runs
                    JOIN steps ON steps.run_id = runs.id
                    WHERE runs.id = ?
                    ORDER BY log_id ASC
                ''', (run_id, last_log_id, last_log_timestamp, run_id))

                # print(f"STREAM: Before Fetch: last_log_id: {last_log_id} ### last_log_timestamp: {last_log_timestamp} ### run_id: {run_id}")
                results = c.fetchall()
                # print(f"STREAM: After Fetch: results: {results}")

                status_data = {
                    'type': 'status',
                    'run_status': None,
                    'steps': []
                }

                for row in results:
                    # print(f"STREAM: row[0]: {row[0]}")
                    if row[0] == 'log':
                        last_log_id = row[1]
                        log_data = {
                            'type': 'log',
                            'step': row[2],
                            'message': row[3]
                        }
                        # print(f"STREAM: yielding log_data: {log_data}")
                        yield f'data: {json.dumps(log_data)}\n\n'
                    elif row[0] == 'status':
                        status_data['run_status'] = row[5]
                        status_data['steps'].append((row[2], row[6]))

                # Only yield status if it has changed
                if status_data != last_status:
                    yield f'data: {json.dumps(status_data)}\n\n'
                    last_status = status_data.copy()

            time.sleep(0.5)  # Keep this for responsiveness, but we're not sending unnecessary updates now

            # Check if the run has completed
            if status_data['run_status'] in ['Completed', 'Failed']:
                break

    return Response(event_stream(), mimetype="text/event-stream")



# Get run status and progress
@app.route('/status/<int:run_id>')
@login_required
def status(run_id):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute('SELECT status FROM runs WHERE id=?', (run_id,))
    run_status = c.fetchone()[0]
    c.execute('SELECT name, status FROM steps WHERE run_id=? ORDER BY id ASC', (run_id,))
    steps = c.fetchall()
    conn.close()
    return jsonify({'run_status': run_status, 'steps': steps})

# Historical runs
@app.route('/history')
@login_required
def history():
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute('SELECT id, start_time FROM runs ORDER BY id DESC')
    runs = c.fetchall()
    conn.close()
    return render_template('history.html', runs=runs)

# View a specific run
@app.route('/view_run/<int:run_id>')
@login_required
def view_run(run_id):
    return render_template('view_run.html', run_id=run_id)


@app.route('/get_checks')
def get_checks():
    """
    Retrieves check data (DataFrames) and renders an HTML page with a table.
    """
    from Check.Manage_Check import extract_image_urls
    checks_df = get_table_from_GZ("tb_case_check")
    results_df = get_table_from_GZ("tb_case_check_result")

    # Merge DataFrames
    merged_df = pd.merge(checks_df, results_df, how='left', left_on='id', right_on='check_id')
    merged_df = merged_df.rename(columns={'id_x': 'id', 'id_y': 'result_id'})

    # Convert 'result' column from JSON string to dictionary
    merged_df['result'] = merged_df['result'].apply(lambda x: json.loads(x) if pd.notna(x) else None)

    # Convert image columns from JSON string to list of urls
    image_columns = ['product_images', 'other_product_images', 'images', 'reference_images']
    for col in image_columns:
        merged_df[col] = merged_df[col].apply(extract_image_urls)

    # Convert DataFrame to a list of dictionaries for easy Jinja templating
    combined_data = merged_df.to_dict('records')

    return render_template('checks_table.html', data=combined_data)


@app.route('/view_check_logs/<string:check_id>')
def view_check_logs(check_id):
    return render_template('view_check_logs.html', check_id=check_id)

@app.route('/get_check_logs/<string:check_id>')
def get_check_logs(check_id):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute('SELECT timestamp, level, message FROM check_logs WHERE check_id=? ORDER BY timestamp ASC', (check_id,))
    logs = [{'timestamp': row[0], 'level': row[1], 'message': row[2]} for row in c.fetchall()]
    conn.close()
    return jsonify({'logs': logs})


@app.route('/get_logs/<int:run_id>/<int:step_index>')
@login_required
def get_logs(run_id, step_index):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute('SELECT name FROM steps WHERE run_id=? ORDER BY id ASC', (run_id,))
    steps = c.fetchall()
    if step_index < len(steps):
        step_name = steps[step_index][0]
    else:
        return jsonify({'logs': []})
    c.execute('''SELECT message FROM logs
                 WHERE run_id=? AND step_id=(
                    SELECT id FROM steps WHERE run_id=? AND name=?
                 ) ORDER BY id ASC''', (run_id, run_id, step_name))
    logs = [{'message': row[0]} for row in c.fetchall()]
    conn.close()
    return jsonify({'logs': logs})

@app.route('/scheduler-status')
@login_required
def scheduler_status():
    jobs = []
    for job in scheduler.get_jobs():
        jobs.append({
            'id': job.id,
            'next_run_time': str(job.next_run_time),
            'trigger': str(job.trigger)
        })
    return jsonify({
        'running': scheduler.running,
        'jobs': jobs
    })

@app.route('/speed')
def test_speed_gpu():
    from Check.RAG.RAG_Inference import test_speed_clipv2
    test_speed_clipv2()
    return jsonify({'message': 'Speed test completed'})

@app.route('/speed2')
def test_speed_gpu2():
    from Check.RAG.RAG_Inference import test_speed
    test_speed()
    return jsonify({'message': 'Speed test completed'})

@app.route('/visualizer')
@login_required
def visualizer():
    """Render the case data visualizer page"""
    return render_template('visualizer.html')

@app.route('/plaintiff_duplicates')
@login_required
def plaintiff_duplicates():
    """Render the case data visualizer page"""
    return render_template('plaintiff_duplicates.html')

@app.route('/plaintiffs')
@login_required
def plaintiffs():
    """Render the plaintiffs management page"""
    return render_template('plaintiffs.html')

@app.route('/review_plaintiff')
@login_required
def review_plaintiff():
    """Render the review plaintiff page"""
    return render_template('review_plaintiff.html')


if __name__ == '__main__':
    init_db()

    # from Check.Do_Check import download_and_check # Only for the main app.py run, not for spawn processes
    # from Check.RAG.RAG_Inference import load_all_models
    # load_all_models()  # Load the tensor flow model and other variables. Only for the main app.py run, not for spawn processes

    # from Check.Data_Cache import update_dataframe_cache
    # update_dataframe_cache()

    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.triggers.cron import CronTrigger
    scheduler = BackgroundScheduler(timezone=pytz.UTC)
    # scheduler.add_job(func=run_fetch_task, trigger=CronTrigger(hour=02, minute=0, timezone=pytz.timezone('America/New_York')))
    # scheduler.add_job(func=run_update_task, trigger=CronTrigger(hour=21, minute=0, timezone=pytz.timezone('America/New_York')))
    # scheduler.add_job(func=run_fix_task, trigger=CronTrigger(hour=23, minute=0, timezone=pytz.timezone('America/New_York')))
    # scheduler.start()
    
    # from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
    

    refresh_cached_data()  # cachd

    serve(
        app,
        host="0.0.0.0",
        port=5005,
        threads=10,  # The number of requests Waitress can actively process at the same time. This is your primary concurrency control.
        connection_limit=40,  # The maximum number of connections Waitress will accept, including those being actively processed and those waiting in its internal queue.
        channel_timeout=900
    )
    
    # The interview case is  1:24-cv-23681

